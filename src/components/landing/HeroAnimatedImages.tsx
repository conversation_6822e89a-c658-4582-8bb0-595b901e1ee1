'use client';
import { motion } from 'framer-motion';

const HeroAnimatedImages = ({serviceImage, secondaryImage, topSecondaryImage, bottomSecondaryImage}) => {
  const heroServiceImage = serviceImage || '/heroImages/landingHero1.png';
  const heroSecondaryServiceImage = secondaryImage || '/heroImages/landingHero2.png';
  const heroTopSecondaryImage = topSecondaryImage || '/heroImages/landingHero3.png';
  const heroBottomSecondaryImage = bottomSecondaryImage || '/heroImages/landingHero4.png';
  return (
    <div className="relative w-full max-w-2x">
      {/* Main Image */}
      <div className="rounded-2xl shadow-2xl border border-gray-200 overflow-hidden aspect-auto">
        <img
          src={heroServiceImage}
          alt="Service screenshot"
          className="w-full h-full object-cover"
        />
      </div>

      {/* Bottom-left image */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3, ease: "easeOut" }}
        className="absolute -bottom-6 -left-6 z-10"
      >
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden w-72">
          <img src={heroSecondaryServiceImage} alt="Service screenshot" className="w-full h-full object-cover" />
        </div>
      </motion.div>

      {/* Top-center image */}
      <motion.div
        initial={{ opacity: 0, y: -30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1, ease: "easeOut" }}
        className="absolute -top-6 left-1/4 transform -translate-x-1/2 z-10"
      >
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden w-52">
          <img src={heroTopSecondaryImage} alt="Service screenshot" className="w-full h-full object-cover" />
        </div>
      </motion.div>

      {/* Bottom-right image */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.5, ease: "easeOut" }}
        className="absolute -bottom-8 -right-8 z-10"
      >
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden w-72">
          <img src={heroBottomSecondaryImage} alt="Service screenshot" className="w-full h-full object-cover" />
        </div>
      </motion.div>
    </div>
  )
}
export default HeroAnimatedImages;
