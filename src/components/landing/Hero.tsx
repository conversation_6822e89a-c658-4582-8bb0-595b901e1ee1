import {Check} from 'lucide-react';
import {useTranslation} from 'next-i18next';
import Link from "next/link";
import HeroAnimatedImages from "@/components/landing/HeroAnimatedImages";

interface HeroProps {
  title?: string;
  titleHighlight?: string;
  subtitle?: string;
  serviceImage?: string;
  secondaryImage?: string;
  topSecondaryImage?: string;
  bottomSecondaryImage?: string;
}

const Hero = ({title, titleHighlight, subtitle, serviceImage, secondaryImage, topSecondaryImage, bottomSecondaryImage}: HeroProps) => {
  const {t} = useTranslation();

  const heroTitle = title || t('hero.title');
  const heroTitleHighlight = titleHighlight || t('hero.titleHighlight');
  const heroSubtitle = subtitle || t('hero.subtitle');


  return (
    <section className="bg-white h-screen flex items-center overflow-hidden">
      <div className="w-full">
        <div className="grid lg:grid-cols-12 items-center max-w-none h-full">
          {/* Left Column */}
          <div className="lg:col-span-5 px-6 lg:pl-20 py-12">
            <h1 className="text-4xl lg:text-5xl xl:text-6xl font-bold text-gray-900 mb-6 leading-tight">
              {heroTitle}
              <br/>
              <span className="bg-gradient-to-r from-blue-800 to-blue-400 bg-clip-text text-transparent">
                {heroTitleHighlight}
              </span>
            </h1>
            <p className="text-lg lg:text-xl text-gray-600 mb-8 leading-relaxed max-w-lg">
              {heroSubtitle}
            </p>
            <div className="mb-8">
              <Link
                href={'/studio'}
                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                {t('hero.startCreating')}
              </Link>
            </div>
            <div className="flex flex-col sm:flex-row gap-6 text-gray-700">
              {['hero.noCardRequired', 'hero.noVPNRequired', 'hero.noSubscription'].map((key) => (
                <div key={key} className="flex items-center gap-2">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                    <Check className="w-4 h-4 text-blue-600"/>
                  </div>
                  <span className="font-medium">{t(key)}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Right Column */}
          <div className="lg:col-span-7 relative hidden lg:block h-full">
            <div className="relative h-full flex items-center justify-center px-8 py-12">
              {/* Main Image Container */}
              <HeroAnimatedImages
                serviceImage={serviceImage}
                secondaryImage={secondaryImage}
                bottomSecondaryImage={bottomSecondaryImage}
                topSecondaryImage={topSecondaryImage}/>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
