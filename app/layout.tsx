
import { Metadata } from 'next';
// import ClientProviders from './ClientProviders';
import "@/src/index.css";
import React from "react";

//TODO: migrate from index.html
export const metadata: Metadata = {
  title: 'AI Content Generator',
  description: 'Generate amazing content with AI',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="ru">
      <body>
        {/*<ClientProviders>*/}
          {children}
        {/*</ClientProviders>*/}
      </body>
    </html>
  );
}
